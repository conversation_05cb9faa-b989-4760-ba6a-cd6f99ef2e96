<!doctype html>
<html lang="en" xmlns:th="http://www.thymeleaf.org"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity6">

<head>

</head>

<body>
<!-- header -->
<header id="site-header" class="fixed-top">
    <div class="container">
        <nav class="navbar navbar-expand-lg navbar-light">
            <a th:href="@{/home}" class="navbar-brand"><i class="fas fa-graduation-cap"></i>Eazy School
            </a>
            <button class="navbar-toggler collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#navbarScroll" aria-controls="navbarScroll" aria-expanded="false"
                    aria-label="Toggle navigation">
                <span class="navbar-toggler-icon fa icon-expand fa-bars"></span>
                <span class="navbar-toggler-icon fa icon-close fa-times"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarScroll">
                <ul class="navbar-nav ms-auto my-2 my-lg-0 navbar-nav-scroll">
                    <li class="nav-item" sec:authorize="isAnonymous()">
                        <a th:href="@{/home}" class="nav-link" aria-current="page">Home</a>
                    </li>
                    <li class="nav-item" sec:authorize="isAnonymous()">
                        <a th:href="@{/courses}" class="nav-link">Courses</a>
                    </li>
                    <li class="nav-item" sec:authorize="isAnonymous()">
                        <a th:href="@{/contact}" class="nav-link">Contact</a>
                    </li>
                    <li class="nav-item" sec:authorize="isAnonymous()">
                        <a th:href="@{/login}" class="nav-link">LogIn</a>
                    </li>
                    <li class="nav-item" sec:authorize="isAuthenticated()">
                        <a th:href="@{/dashboard}" class="nav-link">Dashboard</a>
                    </li>
                    <li class="nav-item" sec:authorize="isAuthenticated()">
                        <a th:href="@{/logout}" class="nav-link">Logout</a>
                    </li>
                </ul>
            </div>
            <!-- toggle switch for light and dark theme -->
            <div class="cont-ser-position">
                <nav class="navigation">
                    <div class="theme-switch-wrapper">
                        <label class="theme-switch" for="checkbox">
                            <input type="checkbox" id="checkbox">
                            <div class="mode-container">
                                <i class="gg-sun"></i>
                                <i class="gg-moon"></i>
                            </div>
                        </label>
                    </div>
                </nav>
            </div>
            <!-- //toggle switch for light and dark theme -->
        </nav>
    </div>
</header>
<!-- //header -->
</body>

</html>