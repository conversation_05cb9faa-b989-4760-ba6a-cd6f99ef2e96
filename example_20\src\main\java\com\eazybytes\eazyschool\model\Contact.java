package com.eazybytes.eazyschool.model;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.hibernate.validator.constraints.Email;

@Data

public class Contact {

    @NotBlank(message="Name must not be blank")
    @Size(min=3,message="Name Must be at least 3 character long")
    private String name;

    @NotBlank(message="Mobile Number must be 10 digits")
    @Pattern(regexp = "(^$|[0-9]{10})",message="Mobile number must be 10 digits")
    private String mobileNum;

    @NotBlank(message="Email must not be blank")
    @Email(message="please provide a valid email address")
    private String email;

    @NotBlank(message = "Subject Must Not be blank")
    @Size(min=5,message = "Subject must be at least 5 characters long")
    private String subject;

    @NotBlank(message="Message must not be blank")
    @Size(min = 20,message = "Message Must be atleast 10 character Long")
    private String message;

}
