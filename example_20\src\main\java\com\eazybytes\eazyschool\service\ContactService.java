package com.eazybytes.eazyschool.service;


import com.eazybytes.eazyschool.model.Contact;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.context.annotation.RequestScope;

@Service
@Slf4j
//@RequestScope
public class ContactService {

    private int counter=0;

    public void setCounter(int counter) {
        this.counter = counter;
    }

//    public ContactService(){
//        System.out.println("contact service instance created");
//    }

    public int getCounter() {
        return counter;
    }




    public boolean saveContact(Contact contact){
        boolean isSaved=true;

        log.info(contact.toString());

        return isSaved;
    }
}
