package com.eazybytes.eazyschool.service;


import com.eazybytes.eazyschool.model.Contact;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ContactService {

    public boolean saveContact(Contact contact){
        boolean isSaved=true;

        log.info(contact.toString());

        return isSaved;
    }
}
