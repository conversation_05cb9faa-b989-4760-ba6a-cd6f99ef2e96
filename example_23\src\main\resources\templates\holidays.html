<!--
Author: W3layouts
Author URL: http://w3layouts.com
-->
<!doctype html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Eazy School - Best Educational Institute for your Child</title>
    <!-- Google fonts -->
    <link href="//fonts.googleapis.com/css2?family=Ubuntu:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Template CSS Style link -->
    <link rel="stylesheet" href="/assets/css/style-starter.css">
</head>

<body>

<!-- header -->
    <div th:replace="~{header :: header}">...</div>
<!-- //header -->

<!-- inner banner -->
<section class="inner-banner py-5">
    <div class="w3l-breadcrumb py-lg-5">
        <div class="container pt-4 pb-sm-4">
            <h4 class="inner-text-title pt-5">Holidays</h4>
            <ul class="breadcrumbs-custom-path">
                <li><a th:href="@{/home}">Home</a></li>
                <li class="active"><i class="fas fa-angle-right"></i>Holidays</li>
            </ul>
        </div>
    </div>
</section>
<!-- //inner banner -->

<!-- Holidays -->
<section class="w3l-timeline-1 py-5">
    <div class="container py-lg-5 py-4">
        <div class="title-heading-w3 text-center mb-sm-5 mb-4">
            <h5 class="title-small">Eazy School</h5>
            <h3 class="title-style">Awesome Holidays</h3>
        </div>
        <div class="row">
            <div class="col-lg-6" th:if="${festival} == true">
                <h5 class="sub-title-timeline"><i class="fas fa-snowman"></i> Festival Holidays</h5>
                <div class="timeline">
                    <div class="timeline">
                        <div th:each="holiday : ${FESTIVAL}" class="column">
                            <div class="title">
                                <h2 th:text="${holiday.reason}"></h2>
                            </div>
                            <div class="description">
                                <h6 th:text="${holiday.day}" class="fas fa-calendar-alt"><i class="fas fa-calendar-alt"></i></h6>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 mt-lg-0 mt-4" th:if="${federal} == true">
                <h5 class="sub-title-timeline"><i class="fas fa-flag-usa"></i> Federal Holidays </h5>
                <div class="timeline">
                    <div th:each="holiday : ${FEDERAL}" class="column">
                        <div class="title">
                            <h2 th:text="${holiday.reason}"></h2>
                        </div>
                        <div class="description">
                            <h6 th:text="${holiday.day}" class="fas fa-calendar-alt"><i class="fas fa-calendar-alt"></i></h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- // Holidays -->

<!-- footer block -->
    <div th:replace="~{footer :: footer}">...</div>
<!-- //footer block -->

<!-- Js scripts -->
<!-- move top -->
<button onclick="topFunction()" id="movetop" title="Go to top">
    <span class="fas fa-level-up-alt" aria-hidden="true"></span>
</button>
<script>
        // When the user scrolls down 20px from the top of the document, show the button
        window.onscroll = function () {
            scrollFunction()
        };

        function scrollFunction() {
            if (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20) {
                document.getElementById("movetop").style.display = "block";
            } else {
                document.getElementById("movetop").style.display = "none";
            }
        }

        // When the user clicks on the button, scroll to the top of the document
        function topFunction() {
            document.body.scrollTop = 0;
            document.documentElement.scrollTop = 0;
        }
    </script>
<!-- //move top -->

<!-- common jquery plugin -->
<script src="/assets/js/jquery-3.3.1.min.js"></script>
<!-- //common jquery plugin -->

<!-- counter-->
<script src="/assets/js/counter.js"></script>
<!-- //counter-->

<!-- theme switch js (light and dark)-->
<script src="/assets/js/theme-change.js"></script>
<!-- //theme switch js (light and dark)-->

<!-- MENU-JS -->
<script>
        $(window).on("scroll", function () {
            var scroll = $(window).scrollTop();

            if (scroll >= 80) {
                $("#site-header").addClass("nav-fixed");
            } else {
                $("#site-header").removeClass("nav-fixed");
            }
        });

        //Main navigation Active Class Add Remove
        $(".navbar-toggler").on("click", function () {
            $("header").toggleClass("active");
        });
        $(document).on("ready", function () {
            if ($(window).width() > 991) {
                $("header").removeClass("active");
            }
            $(window).on("resize", function () {
                if ($(window).width() > 991) {
                    $("header").removeClass("active");
                }
            });
        });
    </script>
<!-- //MENU-JS -->

<!-- disable body scroll which navbar is in active -->
<script>
        $(function () {
            $('.navbar-toggler').click(function () {
                $('body').toggleClass('noscroll');
            })
        });
    </script>
<!-- //disable body scroll which navbar is in active -->

<!-- bootstrap -->
<script src="/assets/js/bootstrap.min.js"></script>
<!-- //bootstrap -->
<!-- //Js scripts -->
</body>

</html>